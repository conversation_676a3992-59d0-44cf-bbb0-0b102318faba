# Télécharger tout (comportement par défaut)
python retrieve_data_asset.py

# Seulement les images couleur + fichiers libres
python retrieve_data_asset.py --data_selection endviews_colour

# Seulement les images grayscale + fichiers libres  
python retrieve_data_asset.py --data_selection endviews_grayscale

# Seulement les données volumétriques + fichiers libres
python retrieve_data_asset.py --data_selection volume_data

# Les deux types d'images + fichiers libres
python retrieve_data_asset.py --data_selection endviews_both

# Avec un baseline et répertoire personnalisés
python retrieve_data_asset.py --baseline custom_baseline.yml --output_dir ./my_data --data_selection endviews_colour