import os
import shutil

# 🔧 Dossier de sortie
ROOT_DIR = r"C:\Users\<USER>\Documents\4Corrosion\azure_data_asset\data\TPI_calibprod_light\validation"

# 🔧 Dossier racine
OUTPUT_DIR = r"C:\Users\<USER>\Documents\4Corrosion\azure_data_asset\output"

def find_and_copy_volume_data(root_dir, output_dir):
    os.makedirs(output_dir, exist_ok=True)

    found_any = False

    for dirpath, dirnames, filenames in os.walk(root_dir):
        print(f"🔎 Exploration : {dirpath}")
        if os.path.basename(dirpath).lower() == "volume_data":
            parent_folder = os.path.basename(os.path.dirname(dirpath))
            print(f"📁 Dossier trouvé : {dirpath} (parent: {parent_folder})")
            for filename in filenames:
                src_path = os.path.join(dirpath, filename)
                new_filename = f"{parent_folder}_{filename}"
                dst_path = os.path.join(output_dir, new_filename)
                shutil.copy2(src_path, dst_path)
                print(f"✅ Copié : {src_path} → {dst_path}")
                found_any = True

    if not found_any:
        print("⚠️ Aucun dossier 'volume_data' trouvé.")

if __name__ == "__main__":
    print("🚀 Démarrage du script...")
    find_and_copy_volume_data(ROOT_DIR, OUTPUT_DIR)
