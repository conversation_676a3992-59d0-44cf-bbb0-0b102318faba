{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# import required libraries\n", "from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential\n", "from azure.ai.ml import MLClient"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["credential = DefaultAzureCredential()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Exception in thread Thread-8 (_readerthread):\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\threading.py\", line 1045, in _bootstrap_inner\n", "    self.run()\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 766, in run_closure\n", "    _threading_Thread_run(self)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\threading.py\", line 982, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\subprocess.py\", line 1599, in _readerthread\n", "    buffer.append(fh.read())\n", "                  ^^^^^^^^^\n", "  File \"<frozen codecs>\", line 322, in decode\n", "UnicodeDecodeError: 'utf-8' codec can't decode byte 0x82 in position 82: invalid start byte\n", "DefaultAzureCredential failed to retrieve a token from the included credentials.\n", "Attempted credentials:\n", "\tEnvironmentCredential: EnvironmentCredential authentication unavailable. Environment variables are not fully configured.\n", "Visit https://aka.ms/azsdk/python/identity/environmentcredential/troubleshoot to troubleshoot this issue.\n", "\tManagedIdentityCredential: ManagedIdentityCredential authentication unavailable, no response from the IMDS endpoint.\n", "\tSharedTokenCacheCredential: SharedTokenCacheCredential authentication unavailable. No accounts were found in the cache.\n", "\tAzureCliCredential: Azure CLI not found on path\n", "\tAzurePowerShellCredential: Failed to invoke PowerShell.\n", "To mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/powershellcredential/troubleshoot.\n", "\tAzureDeveloperCliCredential: Azure Developer CLI could not be found. Please visit https://aka.ms/azure-dev for installation instructions and then,once installed, authenticate to your Azure account using 'azd auth login'.\n", "To mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/defaultazurecredential/troubleshoot.\n"]}, {"ename": "ClientAuthenticationError", "evalue": "DefaultAzureCredential failed to retrieve a token from the included credentials.\nAttempted credentials:\n\tEnvironmentCredential: EnvironmentCredential authentication unavailable. Environment variables are not fully configured.\nVisit https://aka.ms/azsdk/python/identity/environmentcredential/troubleshoot to troubleshoot this issue.\n\tManagedIdentityCredential: ManagedIdentityCredential authentication unavailable, no response from the IMDS endpoint.\n\tSharedTokenCacheCredential: SharedTokenCacheCredential authentication unavailable. No accounts were found in the cache.\n\tAzureCliCredential: Azure CLI not found on path\n\tAzurePowerShellCredential: Failed to invoke PowerShell.\nTo mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/powershellcredential/troubleshoot.\n\tAzureDeveloperCliCredential: Azure Developer CLI could not be found. Please visit https://aka.ms/azure-dev for installation instructions and then,once installed, authenticate to your Azure account using 'azd auth login'.\nTo mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/defaultazurecredential/troubleshoot.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mClientAuthenticationError\u001b[0m                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mcredential\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_token\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mhttps://management.azure.com/.default\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\identity\\_credentials\\default.py:225\u001b[0m, in \u001b[0;36mDefaultAzureCredential.get_token\u001b[1;34m(self, claims, tenant_id, *scopes, **kwargs)\u001b[0m\n\u001b[0;32m    223\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m token\n\u001b[0;32m    224\u001b[0m within_dac\u001b[38;5;241m.\u001b[39mset(\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m--> 225\u001b[0m token \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_token\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mscopes\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclaims\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclaims\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtenant_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtenant_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    226\u001b[0m within_dac\u001b[38;5;241m.\u001b[39mset(\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[0;32m    227\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m token\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\identity\\_credentials\\chained.py:124\u001b[0m, in \u001b[0;36mChainedTokenCredential.get_token\u001b[1;34m(self, claims, tenant_id, *scopes, **kwargs)\u001b[0m\n\u001b[0;32m    116\u001b[0m message \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    117\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\n\u001b[0;32m    118\u001b[0m     \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m failed to retrieve a token from the included credentials.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    121\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://aka.ms/azsdk/python/identity/defaultazurecredential/troubleshoot.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    122\u001b[0m )\n\u001b[0;32m    123\u001b[0m _LOGGER\u001b[38;5;241m.\u001b[39mwarning(message)\n\u001b[1;32m--> 124\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m ClientAuthenticationError(message\u001b[38;5;241m=\u001b[39mmessage)\n", "\u001b[1;31mClientAuthenticationError\u001b[0m: DefaultAzureCredential failed to retrieve a token from the included credentials.\nAttempted credentials:\n\tEnvironmentCredential: EnvironmentCredential authentication unavailable. Environment variables are not fully configured.\nVisit https://aka.ms/azsdk/python/identity/environmentcredential/troubleshoot to troubleshoot this issue.\n\tManagedIdentityCredential: ManagedIdentityCredential authentication unavailable, no response from the IMDS endpoint.\n\tSharedTokenCacheCredential: SharedTokenCacheCredential authentication unavailable. No accounts were found in the cache.\n\tAzureCliCredential: Azure CLI not found on path\n\tAzurePowerShellCredential: Failed to invoke PowerShell.\nTo mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/powershellcredential/troubleshoot.\n\tAzureDeveloperCliCredential: Azure Developer CLI could not be found. Please visit https://aka.ms/azure-dev for installation instructions and then,once installed, authenticate to your Azure account using 'azd auth login'.\nTo mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/defaultazurecredential/troubleshoot."]}], "source": ["credential.get_token(\"https://management.azure.com/.default\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Exception in thread Thread-6 (_readerthread):\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\threading.py\", line 1045, in _bootstrap_inner\n", "    self.run()\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 766, in run_closure\n", "    _threading_Thread_run(self)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\threading.py\", line 982, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\subprocess.py\", line 1599, in _readerthread\n", "    buffer.append(fh.read())\n", "                  ^^^^^^^^^\n", "  File \"<frozen codecs>\", line 322, in decode\n", "UnicodeDecodeError: 'utf-8' codec can't decode byte 0x82 in position 82: invalid start byte\n", "DefaultAzureCredential failed to retrieve a token from the included credentials.\n", "Attempted credentials:\n", "\tEnvironmentCredential: EnvironmentCredential authentication unavailable. Environment variables are not fully configured.\n", "Visit https://aka.ms/azsdk/python/identity/environmentcredential/troubleshoot to troubleshoot this issue.\n", "\tManagedIdentityCredential: ManagedIdentityCredential authentication unavailable, no response from the IMDS endpoint.\n", "\tSharedTokenCacheCredential: SharedTokenCacheCredential authentication unavailable. No accounts were found in the cache.\n", "\tAzureCliCredential: Azure CLI not found on path\n", "\tAzurePowerShellCredential: Failed to invoke PowerShell.\n", "To mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/powershellcredential/troubleshoot.\n", "\tAzureDeveloperCliCredential: Azure Developer CLI could not be found. Please visit https://aka.ms/azure-dev for installation instructions and then,once installed, authenticate to your Azure account using 'azd auth login'.\n", "To mitigate this issue, please refer to the troubleshooting guidelines here at https://aka.ms/azsdk/python/identity/defaultazurecredential/troubleshoot.\n"]}], "source": ["try:\n", "    credential = DefaultAzureCredential()\n", "    # Check if given credential can get token successfully.\n", "    credential.get_token(\"https://management.azure.com/.default\")\n", "except Exception as ex:\n", "    # Fall back to InteractiveBrowserCredential in case DefaultAzureCredential not work\n", "    credential = InteractiveBrowserCredential()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Found the config file in: .\\config.json\n"]}], "source": ["ml_client = MLClient.from_config(credential=credential)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# List all asset in baseline"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from omegaconf import DictConfig, OmegaConf\n", "cfg = OmegaConf.load('./boeing_baseline_3.yml')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['boeing_l1c_2022_10_25_r1_g5:7',\n", " 'boeing_l1c_2022_10_25_r1_g6:5',\n", " 'boeing_l1c_2022_10_25_r1_g8:5',\n", " 'boeing_t1c_2022_10_22_r1_g4:5',\n", " 'boeing_t1c_2022_10_22_r1_g5:7',\n", " 'boeing_t1c_2022_10_22_r1_g6:5',\n", " 'boeing_t1c_2022_10_22_r1_g8:5',\n", " 'boeing_t1c_2023_01_29_r1_g4:3',\n", " 'boeing_t1c_2023_01_29_r1_g5:4',\n", " 'boeing_t1c_2023_01_29_r1_g6:5',\n", " 'boeing_t1c_2023_01_29_r1_g8:4',\n", " 'boeing_la1c_2023_03_06_r1_g5:5',\n", " 'boeing_la1c_2023_03_06_r1_g6:7',\n", " 'boeing_la1c_2023_03_06_r1_g8:5',\n", " 'boeing_lo1c_2023_03_06_r1_g4:5',\n", " 'boeing_lo1c_2023_03_06_r1_g5:5',\n", " 'boeing_lo1c_2023_03_06_r1_g8:5',\n", " 'boeing_la1c_2023_03_06_r4_g5:4',\n", " 'boeing_la1c_2023_03_06_r4_g6:4',\n", " 'boeing_la1c_2023_03_06_r4_g8:4']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["list(cfg['training'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Download 1 asset"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Asset Name : boeing_l1c_2022_10_25_r1_g6, Asset Version : 5\n"]}], "source": ["asset_name, asset_version = cfg[\"training\"][1].split(':')\n", "print(f\"Asset Name : {asset_name}, Asset Version : {asset_version}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Download files_dict\n", "dataobject = ml_client.data.get(asset_name, version=asset_version)\n", "base_path = dataobject.path"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 6\u001b[0m\n\u001b[0;32m      4\u001b[0m (ds, prefix )\u001b[38;5;241m=\u001b[39m get_ds_name_and_path_prefix(base_path)\n\u001b[0;32m      5\u001b[0m dsobject \u001b[38;5;241m=\u001b[39m ml_client\u001b[38;5;241m.\u001b[39mdatastores\n\u001b[1;32m----> 6\u001b[0m \u001b[43mArtifacts\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_artifact_utilities\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload_artifact_from_aml_uri\u001b[49m\u001b[43m(\u001b[49m\u001b[43muri\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mbase_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdestination\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m./\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43masset_name\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m_v\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43masset_version\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m/\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdatastore_operation\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mdsobject\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\ai\\ml\\_artifacts\\_artifact_utilities.py:353\u001b[0m, in \u001b[0;36mdownload_artifact_from_aml_uri\u001b[1;34m(uri, destination, datastore_operation)\u001b[0m\n\u001b[0;32m    344\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Downloads artifact pointed to by URI of the form `azureml://...` to destination.\u001b[39;00m\n\u001b[0;32m    345\u001b[0m \n\u001b[0;32m    346\u001b[0m \u001b[38;5;124;03m:param str uri: AzureML uri of artifact to download\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    350\u001b[0m \u001b[38;5;124;03m:rtype: str\u001b[39;00m\n\u001b[0;32m    351\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    352\u001b[0m parsed_uri \u001b[38;5;241m=\u001b[39m AzureMLDatastorePathUri(uri)\n\u001b[1;32m--> 353\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdownload_artifact\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    354\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstarts_with\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparsed_uri\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    355\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdestination\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdestination\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    356\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdatastore_operation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdatastore_operation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    357\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdatastore_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparsed_uri\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdatastore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    358\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\ai\\ml\\_artifacts\\_artifact_utilities.py:306\u001b[0m, in \u001b[0;36mdownload_artifact\u001b[1;34m(starts_with, destination, datastore_operation, datastore_name, datastore_info)\u001b[0m\n\u001b[0;32m    304\u001b[0m     datastore_info \u001b[38;5;241m=\u001b[39m get_datastore_info(datastore_operation, datastore_name)\n\u001b[0;32m    305\u001b[0m storage_client \u001b[38;5;241m=\u001b[39m get_storage_client(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdatastore_info)\n\u001b[1;32m--> 306\u001b[0m \u001b[43mstorage_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstarts_with\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstarts_with\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdestination\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdestination\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    307\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m destination\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\ai\\ml\\_artifacts\\_blob_storage_helper.py:246\u001b[0m, in \u001b[0;36mBlobStorageClient.download\u001b[1;34m(self, starts_with, destination, max_concurrency)\u001b[0m\n\u001b[0;32m    243\u001b[0m     target_path\u001b[38;5;241m.\u001b[39mmkdir(parents\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, exist_ok\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m    244\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[1;32m--> 246\u001b[0m blob_content \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontainer_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload_blob\u001b[49m\u001b[43m(\u001b[49m\u001b[43mitem\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    248\u001b[0m \u001b[38;5;66;03m# check if total size of download has exceeded 100 MB\u001b[39;00m\n\u001b[0;32m    249\u001b[0m \u001b[38;5;66;03m# make sure proper cloud endpoint is used\u001b[39;00m\n\u001b[0;32m    250\u001b[0m cloud \u001b[38;5;241m=\u001b[39m _get_cloud_details()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\tracing\\decorator.py:94\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     92\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[0;32m     93\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m---> 94\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     96\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[0;32m     97\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_container_client.py:1337\u001b[0m, in \u001b[0;36mContainerClient.download_blob\u001b[1;34m(self, blob, offset, length, encoding, **kwargs)\u001b[0m\n\u001b[0;32m   1335\u001b[0m blob_client \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_blob_client(blob) \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[0;32m   1336\u001b[0m kwargs\u001b[38;5;241m.\u001b[39msetdefault(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmerge_span\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m-> 1337\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mblob_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload_blob\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1338\u001b[0m \u001b[43m    \u001b[49m\u001b[43moffset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffset\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1339\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlength\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlength\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1340\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1341\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\tracing\\decorator.py:94\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     92\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[0;32m     93\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m---> 94\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     96\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[0;32m     97\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_blob_client.py:952\u001b[0m, in \u001b[0;36mBlobClient.download_blob\u001b[1;34m(self, offset, length, encoding, **kwargs)\u001b[0m\n\u001b[0;32m    858\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Downloads a blob to the StorageStreamDownloader. The readall() method must\u001b[39;00m\n\u001b[0;32m    859\u001b[0m \u001b[38;5;124;03mbe used to read all the content or readinto() must be used to download the blob into\u001b[39;00m\n\u001b[0;32m    860\u001b[0m \u001b[38;5;124;03ma stream. Using chunks() returns an iterator which allows the user to iterate over the content in chunks.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    945\u001b[0m \u001b[38;5;124;03m        :caption: Download a blob.\u001b[39;00m\n\u001b[0;32m    946\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    947\u001b[0m options \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_download_blob_options(\n\u001b[0;32m    948\u001b[0m     offset\u001b[38;5;241m=\u001b[39moffset,\n\u001b[0;32m    949\u001b[0m     length\u001b[38;5;241m=\u001b[39mlength,\n\u001b[0;32m    950\u001b[0m     encoding\u001b[38;5;241m=\u001b[39mencoding,\n\u001b[0;32m    951\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m--> 952\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mStorageStreamDownloader\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_download.py:367\u001b[0m, in \u001b[0;36mStorageStreamDownloader.__init__\u001b[1;34m(self, clients, config, start_range, end_range, validate_content, encryption_options, max_concurrency, name, container, encoding, download_cls, **kwargs)\u001b[0m\n\u001b[0;32m    357\u001b[0m     initial_request_end \u001b[38;5;241m=\u001b[39m initial_request_start \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_first_get_size \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m    359\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial_range, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial_offset \u001b[38;5;241m=\u001b[39m process_range_and_offset(\n\u001b[0;32m    360\u001b[0m     initial_request_start,\n\u001b[0;32m    361\u001b[0m     initial_request_end,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    364\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_encryption_data\n\u001b[0;32m    365\u001b[0m )\n\u001b[1;32m--> 367\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_initial_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    368\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproperties \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response\u001b[38;5;241m.\u001b[39mproperties\n\u001b[0;32m    369\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproperties\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_download.py:415\u001b[0m, in \u001b[0;36mStorageStreamDownloader._initial_request\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    413\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m retry_active:\n\u001b[0;32m    414\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 415\u001b[0m         location_mode, response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_clients\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mblob\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    416\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mrange\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrange_header\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    417\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrange_get_content_md5\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrange_validation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    418\u001b[0m \u001b[43m            \u001b[49m\u001b[43mvalidate_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    419\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdata_stream_total\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    420\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdownload_stream_current\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    421\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request_options\u001b[49m\n\u001b[0;32m    422\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    424\u001b[0m         \u001b[38;5;66;03m# Check the location we read from to ensure we use the same one\u001b[39;00m\n\u001b[0;32m    425\u001b[0m         \u001b[38;5;66;03m# for subsequent requests.\u001b[39;00m\n\u001b[0;32m    426\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_location_mode \u001b[38;5;241m=\u001b[39m location_mode\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\tracing\\decorator.py:94\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     92\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[0;32m     93\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m---> 94\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     96\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[0;32m     97\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_blob_operations.py:1597\u001b[0m, in \u001b[0;36mBlobOperations.download\u001b[1;34m(self, snapshot, version_id, timeout, range, range_get_content_md5, range_get_content_crc64, request_id_parameter, lease_access_conditions, cpk_info, modified_access_conditions, **kwargs)\u001b[0m\n\u001b[0;32m   1594\u001b[0m _request\u001b[38;5;241m.\u001b[39murl \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39mformat_url(_request\u001b[38;5;241m.\u001b[39murl)\n\u001b[0;32m   1596\u001b[0m _stream \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m-> 1597\u001b[0m pipeline_response: PipelineResponse \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_pipeline\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# pylint: disable=protected-access\u001b[39;49;00m\n\u001b[0;32m   1598\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_request\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_stream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\n\u001b[0;32m   1599\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1601\u001b[0m response \u001b[38;5;241m=\u001b[39m pipeline_response\u001b[38;5;241m.\u001b[39mhttp_response\n\u001b[0;32m   1603\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;241m200\u001b[39m, \u001b[38;5;241m206\u001b[39m]:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:229\u001b[0m, in \u001b[0;36mPipeline.run\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    227\u001b[0m pipeline_request: PipelineRequest[HTTPRequestType] \u001b[38;5;241m=\u001b[39m PipelineRequest(request, context)\n\u001b[0;32m    228\u001b[0m first_node \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_impl_policies[\u001b[38;5;241m0\u001b[39m] \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_impl_policies \u001b[38;5;28;01melse\u001b[39;00m _TransportRunner(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_transport)\n\u001b[1;32m--> 229\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mfirst_node\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpipeline_request\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "    \u001b[1;31m[... skipping similar frames: _SansIOHTTPPolicyRunner.send at line 86 (2 times)]\u001b[0m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\policies\\_redirect.py:197\u001b[0m, in \u001b[0;36mRedirectPolicy.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m    195\u001b[0m original_domain \u001b[38;5;241m=\u001b[39m get_domain(request\u001b[38;5;241m.\u001b[39mhttp_request\u001b[38;5;241m.\u001b[39murl) \u001b[38;5;28;01mif\u001b[39;00m redirect_settings[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    196\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m retryable:\n\u001b[1;32m--> 197\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    198\u001b[0m     redirect_location \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_redirect_location(response)\n\u001b[0;32m    199\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m redirect_location \u001b[38;5;129;01mand\u001b[39;00m redirect_settings[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_shared\\policies.py:529\u001b[0m, in \u001b[0;36mStorageRetryPolicy.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m    527\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m retries_remaining:\n\u001b[0;32m    528\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 529\u001b[0m         response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    530\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m is_retry(response, retry_settings[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmode\u001b[39m\u001b[38;5;124m'\u001b[39m]):\n\u001b[0;32m    531\u001b[0m             retries_remaining \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mincrement(\n\u001b[0;32m    532\u001b[0m                 retry_settings,\n\u001b[0;32m    533\u001b[0m                 request\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mhttp_request,\n\u001b[0;32m    534\u001b[0m                 response\u001b[38;5;241m=\u001b[39mresponse\u001b[38;5;241m.\u001b[39mhttp_response)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_shared\\policies.py:302\u001b[0m, in \u001b[0;36mStorageResponseHook.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m    297\u001b[0m     upload_stream_current \u001b[38;5;241m=\u001b[39m request\u001b[38;5;241m.\u001b[39mcontext\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mupload_stream_current\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m    299\u001b[0m response_callback \u001b[38;5;241m=\u001b[39m request\u001b[38;5;241m.\u001b[39mcontext\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mresponse_callback\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m \\\n\u001b[0;32m    300\u001b[0m     request\u001b[38;5;241m.\u001b[39mcontext\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mraw_response_hook\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response_callback)\n\u001b[1;32m--> 302\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    304\u001b[0m will_retry \u001b[38;5;241m=\u001b[39m is_retry(response, request\u001b[38;5;241m.\u001b[39mcontext\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmode\u001b[39m\u001b[38;5;124m'\u001b[39m))\n\u001b[0;32m    305\u001b[0m \u001b[38;5;66;03m# Auth error could come from Bearer challenge, in which case this request will be made again\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:86\u001b[0m, in \u001b[0;36m_SansIOHTTPPolicyRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m     84\u001b[0m _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_request, request)\n\u001b[0;32m     85\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 86\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:  \u001b[38;5;66;03m# pylint: disable=broad-except\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     _await_result(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_policy\u001b[38;5;241m.\u001b[39mon_exception, request)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\_base.py:118\u001b[0m, in \u001b[0;36m_TransportRunner.send\u001b[1;34m(self, request)\u001b[0m\n\u001b[0;32m    108\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"HTTP transport send method.\u001b[39;00m\n\u001b[0;32m    109\u001b[0m \n\u001b[0;32m    110\u001b[0m \u001b[38;5;124;03m:param request: The PipelineRequest object.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    113\u001b[0m \u001b[38;5;124;03m:rtype: ~azure.core.pipeline.PipelineResponse\u001b[39;00m\n\u001b[0;32m    114\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    115\u001b[0m cleanup_kwargs_for_transport(request\u001b[38;5;241m.\u001b[39mcontext\u001b[38;5;241m.\u001b[39moptions)\n\u001b[0;32m    116\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m PipelineResponse(\n\u001b[0;32m    117\u001b[0m     request\u001b[38;5;241m.\u001b[39mhttp_request,\n\u001b[1;32m--> 118\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sender\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhttp_request\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m,\n\u001b[0;32m    119\u001b[0m     context\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mcontext,\n\u001b[0;32m    120\u001b[0m )\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_shared\\base_client.py:348\u001b[0m, in \u001b[0;36mTransportWrapper.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    347\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msend\u001b[39m(\u001b[38;5;28mself\u001b[39m, request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m--> 348\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_transport\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\storage\\blob\\_shared\\base_client.py:348\u001b[0m, in \u001b[0;36mTransportWrapper.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    347\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msend\u001b[39m(\u001b[38;5;28mself\u001b[39m, request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m--> 348\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_transport\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\azure\\core\\pipeline\\transport\\_requests_basic.py:355\u001b[0m, in \u001b[0;36mRequestsTransport.send\u001b[1;34m(self, request, proxies, **kwargs)\u001b[0m\n\u001b[0;32m    353\u001b[0m         read_timeout \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mread_timeout\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnection_config\u001b[38;5;241m.\u001b[39mread_timeout)\n\u001b[0;32m    354\u001b[0m         timeout \u001b[38;5;241m=\u001b[39m (connection_timeout, read_timeout)\n\u001b[1;32m--> 355\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore\u001b[39;49;00m\n\u001b[0;32m    356\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    357\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    358\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    359\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    360\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfiles\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfiles\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    361\u001b[0m \u001b[43m        \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mconnection_verify\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnection_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    362\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    363\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcert\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mconnection_cert\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnection_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcert\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    364\u001b[0m \u001b[43m        \u001b[49m\u001b[43mallow_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    365\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    366\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\n\u001b[0;32m    367\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    368\u001b[0m     response\u001b[38;5;241m.\u001b[39mraw\u001b[38;5;241m.\u001b[39menforce_content_length \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m    370\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\requests\\sessions.py:579\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    575\u001b[0m prep \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprepare_request(req)\n\u001b[0;32m    577\u001b[0m proxies \u001b[38;5;241m=\u001b[39m proxies \u001b[38;5;129;01mor\u001b[39;00m {}\n\u001b[1;32m--> 579\u001b[0m settings \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmerge_environment_settings\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    580\u001b[0m \u001b[43m    \u001b[49m\u001b[43mprep\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcert\u001b[49m\n\u001b[0;32m    581\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    583\u001b[0m \u001b[38;5;66;03m# Send the request.\u001b[39;00m\n\u001b[0;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[0;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[0;32m    587\u001b[0m }\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\requests\\sessions.py:760\u001b[0m, in \u001b[0;36mSession.merge_environment_settings\u001b[1;34m(self, url, proxies, stream, verify, cert)\u001b[0m\n\u001b[0;32m    757\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtrust_env:\n\u001b[0;32m    758\u001b[0m     \u001b[38;5;66;03m# Set environment's proxies.\u001b[39;00m\n\u001b[0;32m    759\u001b[0m     no_proxy \u001b[38;5;241m=\u001b[39m proxies\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mno_proxy\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mif\u001b[39;00m proxies \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m \u001b[38;5;28;01<PERSON>se\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m--> 760\u001b[0m     env_proxies \u001b[38;5;241m=\u001b[39m \u001b[43mget_environ_proxies\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mno_proxy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mno_proxy\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    761\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m env_proxies\u001b[38;5;241m.\u001b[39mitems():\n\u001b[0;32m    762\u001b[0m         proxies\u001b[38;5;241m.\u001b[39msetdefault(k, v)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\requests\\utils.py:832\u001b[0m, in \u001b[0;36mget_environ_proxies\u001b[1;34m(url, no_proxy)\u001b[0m\n\u001b[0;32m    826\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_environ_proxies\u001b[39m(url, no_proxy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    827\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    828\u001b[0m \u001b[38;5;124;03m    Return a dict of environment proxies.\u001b[39;00m\n\u001b[0;32m    829\u001b[0m \n\u001b[0;32m    830\u001b[0m \u001b[38;5;124;03m    :rtype: dict\u001b[39;00m\n\u001b[0;32m    831\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 832\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mshould_bypass_proxies\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mno_proxy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mno_proxy\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[0;32m    833\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m {}\n\u001b[0;32m    834\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\requests\\utils.py:816\u001b[0m, in \u001b[0;36mshould_bypass_proxies\u001b[1;34m(url, no_proxy)\u001b[0m\n\u001b[0;32m    813\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m set_environ(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mno_proxy\u001b[39m\u001b[38;5;124m\"\u001b[39m, no_proxy_arg):\n\u001b[0;32m    814\u001b[0m     \u001b[38;5;66;03m# parsed.hostname can be `None` in cases such as a file URI.\u001b[39;00m\n\u001b[0;32m    815\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 816\u001b[0m         bypass \u001b[38;5;241m=\u001b[39m \u001b[43mproxy_bypass\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparsed\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhostname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    817\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mTypeError\u001b[39;00m, socket\u001b[38;5;241m.\u001b[39mgaierror):\n\u001b[0;32m    818\u001b[0m         bypass \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\requests\\utils.py:123\u001b[0m, in \u001b[0;36mproxy_bypass\u001b[1;34m(host)\u001b[0m\n\u001b[0;32m    121\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m proxy_bypass_environment(host)\n\u001b[0;32m    122\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 123\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mproxy_bypass_registry\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\azenv\\Lib\\site-packages\\requests\\utils.py:83\u001b[0m, in \u001b[0;36mproxy_bypass_registry\u001b[1;34m(host)\u001b[0m\n\u001b[0;32m     80\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n\u001b[0;32m     82\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 83\u001b[0m     internetSettings \u001b[38;5;241m=\u001b[39m winreg\u001b[38;5;241m.\u001b[39mOpenKey(\n\u001b[0;32m     84\u001b[0m         winreg\u001b[38;5;241m.\u001b[39mHKEY_CURRENT_USER,\n\u001b[0;32m     85\u001b[0m         \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSoftware\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mMicrosoft\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mWindows\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mCurrentVersion\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mInternet Settings\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     86\u001b[0m     )\n\u001b[0;32m     87\u001b[0m     \u001b[38;5;66;03m# ProxyEnable could be REG_SZ or REG_DWORD, normalizing it\u001b[39;00m\n\u001b[0;32m     88\u001b[0m     proxyEnable \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(winreg\u001b[38;5;241m.\u001b[39mQueryValueEx(internetSettings, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mProxyEnable\u001b[39m\u001b[38;5;124m\"\u001b[39m)[\u001b[38;5;241m0\u001b[39m])\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix\n", "import azure.ai.ml._artifacts as Artifacts\n", "(ds, prefix )= get_ds_name_and_path_prefix(base_path)\n", "dsobject = ml_client.datastores\n", "Artifacts._artifact_utilities.download_artifact_from_aml_uri(uri= base_path, destination = f\"./{asset_name}_v{asset_version}/\", datastore_operation = dsobject)"]}], "metadata": {"kernelspec": {"display_name": "AMLv3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}