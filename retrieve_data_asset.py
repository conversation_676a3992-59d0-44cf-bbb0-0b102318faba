import argparse
import os
from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential
from azure.ai.ml import MLClient
from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix
import azure.ai.ml._artifacts as Artifacts
from omegaconf import OmegaConf

def main(args):
    
    credential = InteractiveBrowserCredential()
    ml_client = MLClient.from_config(credential=credential)

    cfg = OmegaConf.load(args.baseline)

    splits = list(cfg.keys())
    baseline_name = args.baseline.split('/')[-1][:-4]
    for split in splits:
        print(f'Beginning {split} split...')
        split_folder = os.path.join(args.output_dir, baseline_name, split)
        os.makedirs(split_folder, exist_ok=True)

        n_split = len(cfg[split])
        for i_asset, asset_info in enumerate(cfg[split]):
            if (split=='testinference') and (i_asset >= 17):
                asset_name, asset_version = asset_info.split(':')
                print(f"({i_asset+1}/{n_split})  Downloading {asset_name} (version : {asset_version})")
                asset_folder = os.path.join(split_folder, f"{asset_name}_v{asset_version}")

                dataobject = ml_client.data.get(asset_name, version=asset_version)
                base_path = dataobject.path

                (ds, prefix )= get_ds_name_and_path_prefix(base_path)
                dsobject = ml_client.datastores
                Artifacts._artifact_utilities.download_artifact_from_aml_uri(uri= base_path, destination = asset_folder, datastore_operation = dsobject)



def parse_args():
    # setup argparse
    parser = argparse.ArgumentParser()

    # add arguments
    parser.add_argument(
        "--baseline",
        type=str,
        default='./boeing_baseline_3.yml',
        help="Path to .yml baseline"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default='./data',
        help='Path to export data'
    )

    args = parser.parse_args()
    return args

if __name__ == "__main__":
    # parse args
    args = parse_args()

    main(args)