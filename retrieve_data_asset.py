import argparse
import os
import shutil
import subprocess
from azure.identity import InteractiveBrowserCredential
from azure.ai.ml import MLClient
from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix
import azure.ai.ml._artifacts as Artifacts
from omegaconf import OmegaConf

def get_download_method():
    """Détermine la méthode de téléchargement disponible."""
    # Pour l'instant, utilisons toujours Azure ML SDK car AzCopy
    # nécessite une conversion d'URI complexe pour Azure ML
    return 'azure_ml'

def download_with_azcopy(source_uri, destination, data_selection):
    """Télécharge avec AzCopy de manière sélective."""
    print(f"    📦 Using AzCopy for selective download...")

    # Créer le dossier de destination
    os.makedirs(destination, exist_ok=True)

    # Définir les patterns à télécharger selon la sélection
    patterns_to_download = []

    # Toujours télécharger les fichiers libres
    free_files = ["*.csv", "*.pkl", "*.h5", "*.npz"]
    patterns_to_download.extend(free_files)

    # Ajouter les dossiers selon la sélection
    if data_selection == "all":
        patterns_to_download.extend(["endviews_colour/*", "endviews_grayscale/*", "volume_data/*"])
    elif data_selection == "endviews_colour":
        patterns_to_download.append("endviews_colour/*")
    elif data_selection == "endviews_grayscale":
        patterns_to_download.append("endviews_grayscale/*")
    elif data_selection == "volume_data":
        patterns_to_download.append("volume_data/*")
    elif data_selection == "endviews_both":
        patterns_to_download.extend(["endviews_colour/*", "endviews_grayscale/*"])

    success = True
    for pattern in patterns_to_download:
        try:
            source_pattern = f"{source_uri.rstrip('/')}/{pattern}"
            cmd = [
                'azcopy', 'copy',
                source_pattern,
                destination,
                '--recursive',
                '--overwrite=ifSourceNewer',
                '--output-type=text'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0 and "no files matched" not in result.stderr.lower():
                print(f"    ⚠️  Warning downloading {pattern}: {result.stderr}")
                success = False
        except subprocess.TimeoutExpired:
            print(f"    ⚠️  Timeout downloading {pattern}")
            success = False
        except Exception as e:
            print(f"    ⚠️  Error downloading {pattern}: {str(e)}")
            success = False

    return success

def download_with_azure_ml(ml_client, asset_name, asset_version, destination, data_selection):
    """Télécharge avec Azure ML SDK puis filtre."""
    print(f"    📦 Using Azure ML SDK for download...")

    try:
        dataobject = ml_client.data.get(asset_name, version=asset_version)
        base_path = dataobject.path

        # Extraction des informations du datastore (nécessaire pour l'API)
        _ = get_ds_name_and_path_prefix(base_path)
        dsobject = ml_client.datastores

        # Téléchargement avec gestion d'erreur améliorée
        Artifacts._artifact_utilities.download_artifact_from_aml_uri(
            uri=base_path,
            destination=destination,
            datastore_operation=dsobject
        )

        # Filtrer après téléchargement si pas "all"
        if data_selection != "all":
            print(f"    🔧 Filtering content to keep only: {data_selection}")
            filter_downloaded_content(destination, data_selection)

        return True

    except KeyboardInterrupt:
        print(f"    ⚠️  Download interrupted by user")
        raise
    except Exception as e:
        print(f"    ❌ Download failed: {str(e)}")
        return False

def filter_downloaded_content(asset_folder, data_selection):
    """
    Filtre le contenu téléchargé selon la sélection de l'utilisateur.
    Garde toujours les fichiers libres (non dans des dossiers).
    """
    # Définir les dossiers disponibles
    available_folders = ["endviews_colour", "endviews_grayscale", "volume_data"]

    # Convertir la sélection en liste
    if data_selection == "endviews_colour":
        keep_folders = ["endviews_colour"]
    elif data_selection == "endviews_grayscale":
        keep_folders = ["endviews_grayscale"]
    elif data_selection == "volume_data":
        keep_folders = ["volume_data"]
    elif data_selection == "endviews_both":
        keep_folders = ["endviews_colour", "endviews_grayscale"]
    else:
        keep_folders = []

    # Supprimer les dossiers non sélectionnés
    for folder_name in available_folders:
        folder_path = os.path.join(asset_folder, folder_name)
        if folder_name not in keep_folders and os.path.exists(folder_path):
            shutil.rmtree(folder_path)
            print(f"    🗑️  Removed {folder_name} folder")

def main(args):

    credential = InteractiveBrowserCredential()
    ml_client = MLClient.from_config(credential=credential)

    cfg = OmegaConf.load(args.baseline)

    splits = list(cfg.keys())
    baseline_name = args.baseline.split('/')[-1][:-4]

    # Déterminer la méthode de téléchargement
    download_method = get_download_method()

    # Statistiques globales
    total_assets = sum(len(cfg[split]) for split in splits)
    downloaded_count = 0
    skipped_count = 0

    print(f"Starting download process for baseline: {baseline_name}")
    print(f"Total assets to process: {total_assets}")
    print(f"Data selection: {args.data_selection}")
    print(f"Download method: {download_method.upper()}")
    if args.data_selection != "all":
        print(f"⚠️  Will download complete assets then filter to keep: {args.data_selection} + free files")
    else:
        print("📦 Will download complete assets (no filtering)")
    print("=" * 60)

    for split in splits:
        print(f'\n🔄 Processing {split} split...')
        split_folder = os.path.join(args.output_dir, baseline_name, split)
        os.makedirs(split_folder, exist_ok=True)

        n_split = len(cfg[split])
        split_downloaded = 0
        split_skipped = 0

        for i_asset, asset_info in enumerate(cfg[split]):
            asset_name, asset_version = asset_info.split(':')
            asset_folder = os.path.join(split_folder, f"{asset_name}_v{asset_version}")

            # Vérifier si le dossier existe déjà
            if os.path.exists(asset_folder) and os.listdir(asset_folder):
                print(f"  ⏭️  ({i_asset+1}/{n_split}) Skipping {asset_name} (version: {asset_version}) - Already exists")
                skipped_count += 1
                split_skipped += 1
                continue

            # Télécharger l'asset
            print(f"  ⬇️  ({i_asset+1}/{n_split}) Downloading {asset_name} (version: {asset_version})")

            try:
                # Utiliser Azure ML SDK directement
                success = download_with_azure_ml(ml_client, asset_name, asset_version, asset_folder, args.data_selection)

                if success:
                    downloaded_count += 1
                    split_downloaded += 1
                    print(f"  ✅ Successfully downloaded {asset_name}")
                else:
                    print(f"  ❌ Failed to download {asset_name}")

            except KeyboardInterrupt:
                print(f"  ⚠️  Download interrupted by user for {asset_name}")
                print(f"\n🛑 Process interrupted. Partial downloads may exist.")
                return
            except Exception as e:
                print(f"  ❌ Error downloading {asset_name}: {str(e)}")

        print(f"  📊 {split} summary: {split_downloaded} downloaded, {split_skipped} skipped")

    print("\n" + "=" * 60)
    print(f"🎉 Download process completed!")
    print(f"📈 Total summary: {downloaded_count} downloaded, {skipped_count} skipped")
    print(f"📁 Output directory: {os.path.join(args.output_dir, baseline_name)}")



def parse_args():
    # setup argparse
    parser = argparse.ArgumentParser()

    # add arguments
    parser.add_argument(
        "--baseline",
        type=str,
        default='./boeing_baseline_3.yml',
        help="Path to .yml baseline"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default='./data',
        help='Path to export data'
    )

    parser.add_argument(
        "--data_selection",
        type=str,
        choices=['all', 'endviews_colour', 'endviews_grayscale', 'volume_data', 'endviews_both'],
        default='all',
        help='Select which data to download: all (default), endviews_colour, endviews_grayscale, volume_data, or endviews_both (colour+grayscale). Free files are always downloaded.'
    )

    args = parser.parse_args()
    return args

if __name__ == "__main__":
    # parse args
    args = parse_args()

    main(args)